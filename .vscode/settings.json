{"files.exclude": {"**/*.g.dart": true, "**/*.freezed.dart": true, "**/.idea": true, "**/.dart_tool": true, "**/.vscode": true, "**/.cursor": true}, "explorer.excludeGitIgnore": false, "explorer.fileNesting.enabled": true, "explorer.fileNesting.expand": false, "explorer.fileNesting.patterns": {"*.dart": "${capture}.g.dart, ${capture}.freezed.dart"}, "debug.toolBarLocation": "docked", "editor.formatOnSave": true, "editor.formatOnPaste": true, "window.zoomLevel": 1.3}