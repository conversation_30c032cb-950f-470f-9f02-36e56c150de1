import 'package:flutter/material.dart';
import '../../styles/palette.dart';
import '../../styles/text_styles.dart';

class UserSettingListItem extends StatelessWidget {
  final String title;
  final String supportingText;
  final IconData leadingIcon;
  final bool hasSubtitle;
  final VoidCallback onTap;

  const UserSettingListItem({
    super.key,
    required this.title,
    required this.supportingText,
    required this.leadingIcon,
    this.hasSubtitle = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final itemHeight = hasSubtitle ? 72.0 : 56.0;

    return InkWell(
      onTap: onTap,
      child: SizedBox(
        height: itemHeight,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              Icon(
                leadingIcon,
                color: Palette.onSurface,
                size: 24,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      title,
                      style: TextStyles.titleMedium.copyWith(
                        color: Palette.black1d,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (hasSubtitle && supportingText.isNotEmpty)
                      Text(
                        supportingText,
                        style: TextStyles.bodyMedium.copyWith(
                          color: Palette.onSurface,
                        ),
                      ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_right_sharp,
                color: Palette.onSurface,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
