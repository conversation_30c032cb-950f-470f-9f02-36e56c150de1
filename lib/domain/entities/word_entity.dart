import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_entity.freezed.dart';
part 'word_entity.g.dart';

@Freezed(toJson: true)
abstract class WordEntity with _$WordEntity {
  const factory WordEntity({
    required String id,
    required String word,
    required LanguageEntity language,
    required String meaning,
    required Spiciness spiciness,
    required List<String> usages,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    String? phonetic,
    @JsonKey(name: 'is_bookmarked') @Default(false) bool isBookmarked,
  }) = _WordEntity;

  factory WordEntity.fromJson(Map<String, dynamic> json) =>
      _$WordEntityFromJson(json);

  static List<WordEntity> fromJsonList(dynamic data) {
    if (data is! List) return [];

    return data
        .whereType<Map<String, dynamic>>()
        .map(WordEntity.fromJson)
        .toList();
  }
}
