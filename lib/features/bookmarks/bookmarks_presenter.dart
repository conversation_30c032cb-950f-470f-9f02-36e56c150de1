import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/bookmarks_contract.dart';

part 'bookmarks_presenter.g.dart';

@riverpod
class BookmarksPresenter extends _$BookmarksPresenter {
  StreamController<BookmarksSideEffect> sideEffects =
      StreamController<BookmarksSideEffect>();

  late final WordUseCase _wordUseCase;
  late final DataChangeTracker _dataChangeTracker;
  late final TTSService _ttsService;

  @override
  BookmarksState build({required Key key}) {
    _wordUseCase = ref.read(wordUseCaseProvider);
    _dataChangeTracker = ref.read(dataChangeTrackerProvider);
    _ttsService = ref.read(ttsServiceProvider);

    ref.onDispose(() => sideEffects.close());

    state = const BookmarksState();

    _fetchBookmarks();

    return state;
  }

  void intentHandler(BookmarksIntent intent) {
    switch (intent) {
      case RetryIntent _:
        _fetchBookmarks();
        break;
      case GoBackIntent _:
        sideEffects.safeAdd(const BookmarksSideEffect.goBack());
        break;
      case final NavigateToWordDetailIntent intent:
        sideEffects
            .safeAdd(BookmarksSideEffect.navigateToWordDetail(intent.wordId));
        break;
      case final RemoveBookmarkIntent intent:
        _removeBookmark(intent.wordId);
        break;
      case ReturnedIntent _:
        if (_dataChangeTracker.shouldRefresh(
          key.toString(),
          TrackingFeatures.bookmark,
        )) {
          _fetchBookmarks();
        }
        break;
      case final PlayPronunciationIntent intent:
        _ttsService.speak(intent.word.word, intent.word.language.locale);
        break;
    }
  }

  Future<void> _fetchBookmarks() async {
    try {
      state = state.copyWith(loadingState: ScreenLoadingState.loading);
      _dataChangeTracker.recordFetch(key.toString());

      final groupedItems = await _wordUseCase.getGroupedBookmarks();

      state = state.copyWith(
        groupedBookmarkItems: groupedItems,
        loadingState: ScreenLoadingState.loaded,
      );
    } catch (e) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }

  Future<void> _removeBookmark(String wordId) async {
    final previousState = state.copyWith();

    try {
      state = state.removeBookmark(wordId);

      final bookmarkStatus = await _wordUseCase.toggleBookmark(wordId, true);

      if (bookmarkStatus) {
        state = previousState;
        sideEffects.safeAdd(
            BookmarksSideEffect.showMessage(Str.current.bookmarkError));
        return;
      }
      sideEffects.safeAdd(
          BookmarksSideEffect.showMessage(Str.current.bookmarkRemovedSuccess));
    } on CussMeException catch (e) {
      state = previousState;
      sideEffects.safeAdd(BookmarksSideEffect.showMessage(e.message));
    }
  }
}

@riverpod
Stream<BookmarksSideEffect> bookmarksSideEffects(Ref ref, {required Key key}) {
  return ref
      .read(bookmarksPresenterProvider(key: key).notifier)
      .sideEffects
      .stream;
}
