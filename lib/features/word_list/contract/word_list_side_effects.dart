import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_list_side_effects.freezed.dart';

@freezed
sealed class WordListSideEffect with _$WordListSideEffect {
  const factory WordListSideEffect.goBack() = GoBackSideEffect;
  const factory WordListSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
  const factory WordListSideEffect.navigateToWordDetail(String wordId) =
      NavigateToWordDetailSideEffect;
  const factory WordListSideEffect.navigateToSignIn() =
      NavigateToSignInSideEffect;
  const factory WordListSideEffect.navigateToBookmarks() =
      NavigateToBookmarksSideEffect;
  const factory WordListSideEffect.showMessageWithAction(
      String message, String actionText) = ShowMessageWithActionSideEffect;
  const factory WordListSideEffect.navigateToSearch() = NavigateToSearchSideEffect;
}
