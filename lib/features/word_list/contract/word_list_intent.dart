import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_list_intent.freezed.dart';

@freezed
sealed class WordListIntent with _$WordListIntent {
  const factory WordListIntent.goBack() = GoBackIntent;
  const factory WordListIntent.retry() = RetryIntent;
  const factory WordListIntent.toggleSpiciness(Spiciness spiciness) =
      ToggleSpicinessIntent;
  const factory WordListIntent.navigateToWordDetail(String wordId) =
      NavigateToWordDetailIntent;
  const factory WordListIntent.toggleBookmark(WordEntity word) =
      ToggleBookmarkIntent;
  const factory WordListIntent.navigateToBookmarks() =
      NavigateToBookmarksIntent;
  const factory WordListIntent.playPronunciation(WordEntity word) =
      PlayPronunciationIntent;
  const factory WordListIntent.navigateToSearch() = NavigateToSearchIntent;
  const factory WordListIntent.returned() = ReturnedIntent;
}
