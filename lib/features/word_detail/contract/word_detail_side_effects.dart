import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'word_detail_side_effects.freezed.dart';

@freezed
sealed class WordDetailSideEffect with _$WordDetailSideEffect {
  const factory WordDetailSideEffect.showMessage(String message) =
      ShowMessageSideEffect;
  const factory WordDetailSideEffect.close() = CloseSideEffect;
  const factory WordDetailSideEffect.navigateToEditSuggestion(WordEntity word) =
      NavigateToEditSuggestionSideEffect;
  const factory WordDetailSideEffect.navigateToReport(WordEntity word) =
      NavigateToReportSideEffect;
  const factory WordDetailSideEffect.navigateToSignIn() =
      NavigateToSignInSideEffect;
  const factory WordDetailSideEffect.navigateToBookmarks() =
      NavigateToBookmarksSideEffect;
  const factory WordDetailSideEffect.showMessageWithAction(
      String message, String actionText) = ShowMessageWithActionSideEffect;
}
