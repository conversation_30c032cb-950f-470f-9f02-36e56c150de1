import 'dart:async';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/word_detail_contract.dart';

part 'word_detail_presenter.g.dart';

@riverpod
class WordDetailPresenter extends _$WordDetailPresenter {
  late StreamController<WordDetailSideEffect> sideEffects =
      StreamController<WordDetailSideEffect>();

  late final WordUseCase _wordUseCase;
  late final AuthUseCase _authUseCase;
  late final DataChangeTracker _dataChangeTracker;
  late final TTSService _ttsService;

  @override
  WordDetailState build({required Key key, required String wordId}) {
    _wordUseCase = ref.read(wordUseCaseProvider);
    _authUseCase = ref.read(authUseCaseProvider);
    _dataChangeTracker = ref.read(dataChangeTrackerProvider);
    _ttsService = ref.read(ttsServiceProvider);

    ref.onDispose(() {
      sideEffects.close();
    });

    state = const WordDetailState();

    _checkIfUserIsGuest();
    _fetchWordDetails(wordId);

    return state;
  }

  Future<void> _checkIfUserIsGuest() async {
    final isGuest = await _authUseCase.isGuest();
    state = state.copyWith(isGuest: isGuest);
  }

  void intentHandler(WordDetailIntent intent) {
    switch (intent) {
      case RetryIntent _:
        _fetchWordDetails(wordId);
        break;
      case CloseIntent _:
        sideEffects.safeAdd(const WordDetailSideEffect.close());
        break;
      case ToggleBookmarkIntent _:
        if (state.word == null) return;
        if (state.isGuest) {
          sideEffects.safeAdd(const WordDetailSideEffect.navigateToSignIn());
          return;
        }
        _toggleBookmark(state.word!);
        break;
      case final PlayPronunciationIntent intent:
        _ttsService.speak(intent.word.word, intent.word.language.locale);
        break;
      case SuggestEditIntent _:
        if (state.word == null) return;
        if (state.isGuest) {
          sideEffects.safeAdd(const WordDetailSideEffect.navigateToSignIn());
          return;
        }
        sideEffects.safeAdd(
            WordDetailSideEffect.navigateToEditSuggestion(state.word!));
        break;
      case ReportInaccuracyIntent _:
        if (state.word == null) return;
        if (state.isGuest) {
          sideEffects.safeAdd(const WordDetailSideEffect.navigateToSignIn());
          return;
        }
        sideEffects.safeAdd(WordDetailSideEffect.navigateToReport(state.word!));
        break;
      case NavigateToBookmarksIntent _:
        sideEffects.safeAdd(const WordDetailSideEffect.navigateToBookmarks());
        break;
    }
  }

  Future<void> _fetchWordDetails(String wordId) async {
    try {
      state = state.copyWith(loadingState: ScreenLoadingState.loading);

      final word = await _wordUseCase.getWordById(wordId);

      state = state.copyWith(
        word: word,
        loadingState: ScreenLoadingState.loaded,
      );
    } catch (e) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }

  Future<void> _toggleBookmark(WordEntity word) async {
    try {
      final updatedWord = word.copyWith(isBookmarked: !word.isBookmarked);
      state = state.copyWith(word: updatedWord);
      _dataChangeTracker.recordUpdate(TrackingFeatures.bookmark);

      final newBookmarkStatus = await _wordUseCase.toggleBookmark(
        word.id,
        word.isBookmarked,
      );

      sideEffects.safeAdd(WordDetailSideEffect.showMessageWithAction(
          newBookmarkStatus
              ? Str.current.bookmarkAddedSuccess
              : Str.current.bookmarkRemovedSuccess,
          Str.current.allBookmarks));
      if (newBookmarkStatus != updatedWord.isBookmarked) {
        state = state.copyWith(
          word: state.word!.copyWith(isBookmarked: newBookmarkStatus),
        );
      }
    } on CussMeException catch (e) {
      state = state.copyWith(word: word);
      sideEffects.safeAdd(WordDetailSideEffect.showMessage(e.message));
    }
  }
}

@riverpod
Stream<WordDetailSideEffect> wordDetailSideEffects(Ref ref,
    {required Key key, required String wordId}) {
  return ref
      .watch(wordDetailPresenterProvider(wordId: wordId, key: key).notifier)
      .sideEffects
      .stream;
}
