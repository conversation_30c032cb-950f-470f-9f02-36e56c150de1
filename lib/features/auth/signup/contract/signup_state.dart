import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_state.freezed.dart';

@freezed
sealed class SignUpState with _$SignUpState {
  const factory SignUpState({
    @Default('') String firstName,
    @Default('') String lastName,
    @Default('') String email,
    @Default('') String password,
    @Default('') String confirmPassword,
    @Default(false) bool isPasswordVisible,
    @Default(false) bool isConfirmPasswordVisible,
    @Default(false) bool isEmailLoading,
    @Default(false) bool isGoogleLoading,
    @Default(false) bool isFacebookLoading,
    @Default(null) String? firstNameError,
    @Default(null) String? lastNameError,
    @Default(null) String? emailError,
    @Default(null) String? passwordError,
    @Default(null) String? confirmPasswordError,
    @Default(false) bool emailAlreadyExists,
  }) = _SignUpState;
}
