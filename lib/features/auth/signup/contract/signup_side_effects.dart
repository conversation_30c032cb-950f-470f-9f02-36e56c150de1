import 'package:freezed_annotation/freezed_annotation.dart';

part 'signup_side_effects.freezed.dart';

@freezed
sealed class SignUpSideEffect with _$SignUpSideEffect {
  const factory SignUpSideEffect.navigateToHome() = NavigateToHomeSideEffect;
  const factory SignUpSideEffect.navigateToGuestHome() =
      NavigateToGuestHomeSideEffect;
  const factory SignUpSideEffect.navigateToSignIn() =
      NavigateToSignInSideEffect;
  const factory SignUpSideEffect.showError(String message) =
      ShowErrorSideEffect;
  const factory SignUpSideEffect.hideKeyboard() = HideKeyboardSideEffect;
  const factory SignUpSideEffect.navigateToIntro() = NavigateToIntroSideEffect;
}
