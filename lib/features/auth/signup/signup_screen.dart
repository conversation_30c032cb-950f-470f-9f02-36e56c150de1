import 'package:cussme/routing/app_router.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../localization/generated/l10n.dart';
import 'contract/signup_contract.dart';
import 'signup_presenter.dart';

class SignUpScreen extends ConsumerWidget {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    final str = Str.of(context);
    final state = ref.watch(signUpPresenterProvider);
    final presenter = ref.read(signUpPresenterProvider.notifier);

    return AdMobScaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          return SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: constraints.maxHeight,
              ),
              child: IntrinsicHeight(
                child: Column(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const SizedBox(height: 28),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  str.signUp,
                                  style: TextStyles.headlineSmall.copyWith(
                                    color: Palette.primary,
                                  ),
                                ),
                                SvgPicture.asset(
                                  'assets/images/ic_cussme.svg',
                                  height: 23,
                                ),
                              ],
                            ),
                            const SizedBox(height: 18),
                            const Spacer(),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (state.emailAlreadyExists)
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: 6, bottom: 24),
                                        child: Text(str.emailAlreadyExists,
                                            style: TextStyles.bodyMedium
                                                .copyWith(
                                                    color: Palette.primary)),
                                      ),
                                    CustomTextField(
                                      label: str.firstNameLabel,
                                      errorText: state.firstNameError,
                                      onChanged: (value) =>
                                          presenter.intentHandler(
                                        SignUpIntent.firstNameChanged(value),
                                      ),
                                      textCapitalization:
                                          TextCapitalization.words,
                                      textInputAction: TextInputAction.next,
                                    ),
                                    const SizedBox(height: 12),
                                    CustomTextField(
                                      label: str.lastNameLabel,
                                      errorText: state.lastNameError,
                                      onChanged: (value) =>
                                          presenter.intentHandler(
                                        SignUpIntent.lastNameChanged(value),
                                      ),
                                      textCapitalization:
                                          TextCapitalization.words,
                                      textInputAction: TextInputAction.next,
                                    ),
                                    const SizedBox(height: 12),
                                    CustomTextField(
                                      label: str.emailRequired,
                                      errorText: state.emailError,
                                      onChanged: (value) =>
                                          presenter.intentHandler(
                                        SignUpIntent.emailChanged(value),
                                      ),
                                      keyboardType: TextInputType.emailAddress,
                                      textInputAction: TextInputAction.next,
                                    ),
                                    const SizedBox(height: 12),
                                    CustomTextField(
                                      label: str.passwordRequired,
                                      errorText: state.passwordError,
                                      obscureText: !state.isPasswordVisible,
                                      togglePasswordVisibility: true,
                                      onToggleVisibility: () =>
                                          presenter.intentHandler(
                                        const SignUpIntent
                                            .togglePasswordVisibility(),
                                      ),
                                      onChanged: (value) =>
                                          presenter.intentHandler(
                                        SignUpIntent.passwordChanged(value),
                                      ),
                                      textInputAction: TextInputAction.next,
                                    ),
                                    const SizedBox(height: 12),
                                    CustomTextField(
                                      label: str.retypePasswordLabel,
                                      errorText: state.confirmPasswordError,
                                      obscureText:
                                          !state.isConfirmPasswordVisible,
                                      togglePasswordVisibility: true,
                                      onToggleVisibility: () =>
                                          presenter.intentHandler(
                                        const SignUpIntent
                                            .toggleConfirmPasswordVisibility(),
                                      ),
                                      onChanged: (value) =>
                                          presenter.intentHandler(
                                        SignUpIntent.confirmPasswordChanged(
                                            value),
                                      ),
                                      textInputAction: TextInputAction.done,
                                    ),
                                    const SizedBox(height: 12),
                                    Text(
                                      str.passwordRequirementsDetailed,
                                      style: TextStyles.bodySmall.copyWith(
                                        color: state.passwordError != null
                                            ? Palette.error
                                            : Palette.onSurface,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),
                            PrimaryButton(
                              text: str.signUpButton,
                              onPressed: () => presenter.intentHandler(
                                const SignUpIntent.signUpWithEmail(),
                              ),
                              isLoading: state.isEmailLoading,
                            ),
                            const SizedBox(height: 18),
                            SocialAuthButtons(
                              onGooglePressed: () => presenter.intentHandler(
                                const SignUpIntent.signUpWithGoogle(),
                              ),
                              onFacebookPressed: () => presenter.intentHandler(
                                const SignUpIntent.signUpWithFacebook(),
                              ),
                              isGoogleLoading: state.isGoogleLoading,
                              isFacebookLoading: state.isFacebookLoading,
                            ),
                            const SizedBox(height: 24),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  str.alreadyHaveAccount,
                                  style: TextStyles.labelLarge.copyWith(
                                    color: Palette.primaryDark,
                                  ),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.translucent,
                                  onTap: () => presenter.intentHandler(
                                    const SignUpIntent.navigateToSignIn(),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 8.0, horizontal: 2.0),
                                    child: Text(
                                      str.signInHereText,
                                      style: TextStyles.labelLarge.copyWith(
                                        color: Palette.primary,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 16, 16.0, 20.0),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () => presenter.intentHandler(
                            const SignUpIntent.continueWithoutSignUp(),
                          ),
                          style: TextButton.styleFrom(
                            foregroundColor: Palette.primary,
                            padding: EdgeInsets.zero,
                            visualDensity: VisualDensity.compact,
                            minimumSize: Size.zero,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                str.continueWithoutSignIn,
                                style: TextStyles.labelLarge,
                              ),
                              const SizedBox(width: 4),
                              const Icon(Icons.keyboard_arrow_right, size: 24),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(signUpSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateToHomeSideEffect _:
            GoRouter.of(context).goToHome();
            break;
          case NavigateToGuestHomeSideEffect _:
            GoRouter.of(context).goToGuestHome();
            break;
          case NavigateToSignInSideEffect _:
            try {
              context.pop();
            } catch (e) {
              GoRouter.of(context).goToSignIn();
            }
            break;
          case NavigateToIntroSideEffect _:
            GoRouter.of(context).goToIntro();
            break;
          case final ShowErrorSideEffect intent:
            Toast.show(context, intent.message);
            break;
          case HideKeyboardSideEffect _:
            KeyboardUtils.hideKeyboard(context);
            break;
        }
      });
    });
  }
}
