import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final str = Str.of(context);

    return Dialog(
      backgroundColor: Palette.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(28),
      ),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                      child: Column(
                        children: [
                          SizedBox(
                            height: 50,
                            child: SvgPicture.asset(
                              'assets/images/splash_logo.svg',
                              height: 75,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            str.aboutAppTitle,
                            style: TextStyles.titleLarge.copyWith(
                              fontSize: 24,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 16),
                          Align(
                            alignment: Alignment.centerLeft,
                            child: SizedBox(
                              height: 57,
                              width: 57,
                              child: Image.asset(
                                'assets/images/abishek.png',
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            str.aboutAppDescription,
                            style: TextStyles.bodyMedium.copyWith(
                              color: Palette.onSurface,
                            ),
                            textAlign: TextAlign.left,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            str.specialThanksTo,
                            style: TextStyles.labelLarge.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Palette.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            str.developerText,
                            style: TextStyles.bodyMedium.copyWith(
                              color: Palette.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: TextButton.styleFrom(
                    foregroundColor: Palette.primary,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  child: Text(
                    str.closeButton,
                    style: TextStyles.labelLarge,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void showAppAboutDialog(BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => const AboutScreen(),
  );
}
