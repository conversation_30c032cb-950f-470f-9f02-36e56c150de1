import 'package:cussme/domain/domain.dart';
import 'package:cussme/ui/ui.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_state.freezed.dart';

@freezed
sealed class SearchState with _$SearchState {
  const SearchState._();

  const factory SearchState({
    @Default('') String query,
    @Default([]) List<ListItem> searchResults,
    @Default(ScreenLoadingState.loaded) ScreenLoadingState loadingState,
  }) = _SearchState;

  bool get hasResults => searchResults.isNotEmpty;
}
