import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_side_effects.freezed.dart';

@freezed
sealed class SearchSideEffect with _$SearchSideEffect {
  const factory SearchSideEffect.navigateToWordDetail(String wordId) =
      NavigateToWordDetailSideEffect;
  const factory SearchSideEffect.navigateToWordList(LanguageEntity language) =
      NavigateToWordListSideEffect;
  const factory SearchSideEffect.goBack() = GoBackSideEffect;
  const factory SearchSideEffect.showMessage(String message) = ShowMessageSideEffect;
}
