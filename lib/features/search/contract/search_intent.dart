import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'search_intent.freezed.dart';

@freezed
sealed class SearchIntent with _$SearchIntent {
  const factory SearchIntent.search(String query) = SearchQueryIntent;
  const factory SearchIntent.clearSearch() = ClearSearchIntent;
  const factory SearchIntent.retry() = RetryIntent;
  const factory SearchIntent.goBack() = GoBackIntent;
  const factory SearchIntent.selectWord(String wordId) = SelectWordIntent;
  const factory SearchIntent.selectLanguage(LanguageEntity language) =
      SelectLanguageIntent;
}
