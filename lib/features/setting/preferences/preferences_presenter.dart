import 'dart:async';

import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/preferences_contract.dart';

part 'preferences_presenter.g.dart';

@riverpod
class PreferencesPresenter extends _$PreferencesPresenter {
  final StreamController<PreferencesSideEffect> sideEffects =
      StreamController<PreferencesSideEffect>();

  @override
  PreferencesState build() {
    ref.onDispose(() => sideEffects.close());
    return const PreferencesState(initialized: true);
  }

  void intentHandler(PreferencesIntent intent) {
    switch (intent) {
      case NavigateToChooseLanguageIntent _:
        sideEffects
            .safeAdd(const PreferencesSideEffect.navigateToChooseLanguage());
        break;
      case NavigateToChooseApicinessIntent _:
        sideEffects
            .safeAdd(const PreferencesSideEffect.navigateToChooseSpiciness());
        break;
      case NavigateToAccountSettingsIntent _:
        sideEffects
            .safeAdd(const PreferencesSideEffect.navigateToAccountSettings());
        break;
      case GoBackIntent _:
        sideEffects.safeAdd(const PreferencesSideEffect.goBack());
        break;
    }
  }
}

@riverpod
Stream<PreferencesSideEffect> preferencesSideEffects(Ref ref) {
  return ref.read(preferencesPresenterProvider.notifier).sideEffects.stream;
}
