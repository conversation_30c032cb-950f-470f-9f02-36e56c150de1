import 'package:cussme/routing/app_router.dart';
import 'package:cussme/routing/route_constants.dart';
import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../localization/generated/l10n.dart';
import 'contract/preferences_contract.dart';
import 'preferences_presenter.dart';

class PreferencesScreen extends ConsumerWidget {
  const PreferencesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    _handleSideEffects(context, ref);
    ref.watch(preferencesPresenterProvider);
    final presenter = ref.read(preferencesPresenterProvider.notifier);

    return AdMobScaffold(
      body: Column(
        children: [
          CussMeHeader(
            title: Str.of(context).preferencesTitle,
            titleIcon: Icons.settings_outlined,
            backButtonText: Str.of(context).backButtonText,
            isLargeTitle: true,
            onBackPressed: () =>
                presenter.intentHandler(const PreferencesIntent.goBack()),
          ),
          _buildListItems(context, presenter),
        ],
      ),
    );
  }

  Widget _buildListItems(BuildContext context, PreferencesPresenter presenter) {
    return Column(
      children: [
        UserSettingListItem(
          title: Str.of(context).chooseLanguageLabel,
          supportingText: Str.of(context).chooseLanguageSupportingText,
          leadingIcon: Icons.language,
          onTap: () => presenter.intentHandler(
            const PreferencesIntent.navigateToChooseLanguage(),
          ),
        ),
        const Divider(height: 1, color: Palette.outlineVariant),
        UserSettingListItem(
          title: Str.of(context).chooseApicinessLabel,
          supportingText: Str.of(context).chooseSpicinessSupportingText,
          leadingIcon: Icons.local_fire_department,
          onTap: () => presenter.intentHandler(
            const PreferencesIntent.navigateToChooseSpiciness(),
          ),
        ),
        const Divider(height: 1, color: Palette.outlineVariant),
        UserSettingListItem(
          title: Str.of(context).accountSettingsLabel,
          supportingText: Str.of(context).accountSettingsSupportingText,
          leadingIcon: Icons.person_outline,
          hasSubtitle: true,
          onTap: () => presenter.intentHandler(
            const PreferencesIntent.navigateToAccountSettings(),
          ),
        ),
        const Divider(height: 1, color: Palette.outlineVariant),
      ],
    );
  }

  void _handleSideEffects(BuildContext context, WidgetRef ref) {
    ref.listen(preferencesSideEffectsProvider, (_, next) {
      next.whenData((sideEffect) {
        switch (sideEffect) {
          case NavigateToChooseLanguageSideEffect _:
            GoRouter.of(context)
                .pushToLanguages(source: Str.of(context).preferencesTitle);
            break;
          case NavigateToChooseSpicinessSideEffect _:
            GoRouter.of(context).pushToSpiciness();
            break;
          case NavigateToAccountSettingsSideEffect _:
            GoRouter.of(context).pushToAccountSettings();
            break;
          case GoBackSideEffect _:
            Navigator.pop(context);
            break;
          case final ShowErrorSideEffect intent:
            Toast.show(context, intent.message);
            break;
        }
      });
    });
  }
}
