import 'package:freezed_annotation/freezed_annotation.dart';

part 'preferences_intent.freezed.dart';

@freezed
sealed class PreferencesIntent with _$PreferencesIntent {
  const factory PreferencesIntent.navigateToChooseLanguage() =
      NavigateToChooseLanguageIntent;
  const factory PreferencesIntent.navigateToChooseSpiciness() =
      NavigateToChooseApicinessIntent;
  const factory PreferencesIntent.navigateToAccountSettings() =
      NavigateToAccountSettingsIntent;
  const factory PreferencesIntent.goBack() = GoBackIntent;
}
