import 'package:freezed_annotation/freezed_annotation.dart';

part 'preferences_side_effects.freezed.dart';

@freezed
sealed class PreferencesSideEffect with _$PreferencesSideEffect {
  const factory PreferencesSideEffect.navigateToChooseLanguage() =
      NavigateToChooseLanguageSideEffect;
  const factory PreferencesSideEffect.navigateToChooseSpiciness() =
      NavigateToChooseSpicinessSideEffect;
  const factory PreferencesSideEffect.navigateToAccountSettings() =
      NavigateToAccountSettingsSideEffect;
  const factory PreferencesSideEffect.goBack() = GoBackSideEffect;
  const factory PreferencesSideEffect.showError(String message) =
      ShowErrorSideEffect;
}
