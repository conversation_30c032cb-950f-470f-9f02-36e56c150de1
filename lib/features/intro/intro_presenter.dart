import 'dart:async';

import 'package:cussme/data/use_cases/auth_use_case.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/ui/ui.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'contract/intro_contract.dart';

part 'intro_presenter.g.dart';

@riverpod
class IntroPresenter extends _$IntroPresenter {
  late final AuthUseCase _authUseCase;
  StreamController<IntroSideEffect> sideEffects =
      StreamController<IntroSideEffect>();

  @override
  IntroState build() {
    _authUseCase = ref.read(authUseCaseProvider);
    ref.onDispose(() => sideEffects.close());

    state = const IntroState();

    _fetchInitialData();

    return state;
  }

  Future<void> _fetchInitialData() async {
    try {
      state = state.copyWith(loadingState: ScreenLoadingState.loading);

      final allLanguages = await _authUseCase.getAllLanguages();
      final currentUser = await _authUseCase.getCurrentUser();

      state = state.copyWith(
        availableLanguages: allLanguages,
        user: currentUser,
        selectedLanguages: currentUser!.languages,
        selectedSpiciness: currentUser.spiciness,
        loadingState: ScreenLoadingState.loaded,
      );

      _updatePrimaryButtonState();
    } catch (_) {
      state = state.copyWith(loadingState: ScreenLoadingState.error);
    }
  }

  void intentHandler(IntroIntent intent) {
    switch (intent) {
      case final LanguageSelectedIntent intent:
        state = state.copyWith(
            selectedLanguages:
                state.selectedLanguages.toggleItem(intent.language));
        _updatePrimaryButtonState();
        break;
      case final SpicinessToggledIntent intent:
        _toggleSpiciness(intent.spiciness);
        break;
      case SkipToHomeIntent _:
        sideEffects.safeAdd(const IntroSideEffect.navigateToHome());
        break;
      case final PageChangedIntent intent:
        _pageChanged(intent.pageIndex);
        break;
      case PrimaryButtonClickedIntent _:
        if (state.isLanguagePage) {
          _updateLanguages();
        } else {
          _updateSpiciness();
        }
        break;
      case SecondaryButtonClickedIntent _:
        if (state.isLoading) break;
        _togglePage();
        break;
      case RetryIntent _:
        _fetchInitialData();
        break;
    }
  }

  void _togglePage() {
    state =
        state.isLanguagePage ? state.toSpicinessPage() : state.toLanguagePage();

    sideEffects.safeAdd(IntroSideEffect.navigateToPage(state.currentPage));
  }

  void _pageChanged(int pageIndex) {
    state = state.copyWith(currentPage: pageIndex);
    if (state.isLanguagePage) {
      state = state.setSelectedLanguages();
    }
    _updatePrimaryButtonState();
  }

  void _toggleSpiciness(Spiciness spiciness) {
    final currentSpiciness = state.selectedSpiciness.toggleItem(spiciness);

    if (currentSpiciness.isEmpty) {
      sideEffects.safeAdd(
          IntroSideEffect.showMessage(Str.current.spicinessSelectionRequired));
      return;
    }

    state = state.copyWith(selectedSpiciness: currentSpiciness);
    _updatePrimaryButtonState();
  }

  void _updatePrimaryButtonState() {
    bool isEnabled = true;

    if (state.isLanguagePage) {
      isEnabled = state.selectedLanguages.isNotEmpty;
    } else if (state.isSpicinessPage) {
      isEnabled = state.selectedSpiciness.isNotEmpty;
    }

    state = state.copyWith(isPrimaryButtonEnabled: isEnabled);
  }

  void _updateSpiciness() async {
    if (state.selectedSpiciness.isEmpty) return;
    if (state.selectedSpiciness.isEqualTo(state.user!.spiciness)) {
      sideEffects.safeAdd(const IntroSideEffect.navigateToHome());
      return;
    }

    try {
      state = state.copyWith(isLoading: true);

      final user = await _authUseCase.updateSpiciness(state.selectedSpiciness);

      state = state.copyWith(user: user);
      sideEffects.safeAdd(const IntroSideEffect.navigateToHome());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(IntroSideEffect.showMessage(e.message));
      _updatePrimaryButtonState();
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  void _updateLanguages() async {
    if (state.selectedLanguages.isEmpty) return;

    if (state.selectedLanguages.isEqualTo(state.user!.languages)) {
      sideEffects.safeAdd(const IntroSideEffect.navigateToSpiciness());
      return;
    }

    try {
      state = state.copyWith(isLoading: true);

      final selectedLanguages =
          List<LanguageEntity>.from(state.selectedLanguages);
      final user = await _authUseCase.updateLanguages(selectedLanguages);

      state = state.copyWith(user: user);

      sideEffects.safeAdd(const IntroSideEffect.navigateToSpiciness());
    } on CussMeException catch (e) {
      sideEffects.safeAdd(IntroSideEffect.showMessage(e.message));
    } finally {
      state = state.copyWith(isLoading: false);
      _updatePrimaryButtonState();
    }
  }
}

@riverpod
Stream<IntroSideEffect> introSideEffects(Ref ref) {
  return ref.read(introPresenterProvider.notifier).sideEffects.stream;
}
