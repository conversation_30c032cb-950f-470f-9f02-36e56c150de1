import 'package:cussme/data/data.dart';
import 'package:cussme/data/supabase/auth_http_client.dart';
import 'package:cussme/di/general_provider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  final sharedPreferences = await SharedPreferences.getInstance();

  await Supabase.initialize(
    url: supabaseBaseUrl,
    anonKey: supabaseAnonKey,
    httpClient: AuthHttpClient(sharedPreferences),
  );

  await MobileAds.instance.initialize();

  runApp(
    ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWith(
          (ref) => sharedPreferences,
        ),
      ],
      child: const <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
    ),
  );
}
