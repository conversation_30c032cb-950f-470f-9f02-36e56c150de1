import 'route_constants.dart';

enum RouteLocation {
  splash(splashPath, splashName),
  signIn(signInPath, signInName),
  signUp(signUpPath, signUpName),
  forgotPassword(forgotPasswordPath, forgotPasswordName),
  resetPasswordNew(resetPasswordNewPath, resetPasswordNewName),
  resetPasswordSent(resetPasswordSentPath, resetPasswordSentName),
  resetPasswordSuccess(resetPasswordSuccessPath, resetPasswordSuccessName),
  home(homePath, homeName),
  guestHome(guestHomePath, guestHomeName),
  intro(introPath, introName),
  preferences(preferencesPath, preferencesName),
  accountSettings(accountSettingsPath, accountSettingsName),
  spiciness(spicinessPath, spicinessName),
  languages(languagesPath, languagesName),
  wordDetail(wordDetailPath, wordDetailName),
  editSuggestion(editSuggestionPath, editSuggestionName),
  report(reportPath, reportName),
  bookmarks(bookmarksPath, bookmarksName),
  wordList(wordListPath, wordListName),
  search(searchPath, searchName);

  final String path;
  final String name;
  const RouteLocation(this.path, this.name);
}
