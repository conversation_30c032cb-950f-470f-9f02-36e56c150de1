// Route paths
const String splashPath = '/';
const String signInPath = '/sign-in';
const String signUpPath = '/sign-up';
const String forgotPasswordPath = '/forgot-password';
const String resetPasswordNewPath = '/reset-password-new';
const String resetPasswordSentPath = '/reset-password-sent';
const String resetPasswordSuccessPath = '/reset-password-success';
const String homePath = '/home';
const String guestHomePath = '/guest-home';
const String introPath = '/intro';
const String preferencesPath = '/preferences';
const String accountSettingsPath = '/account-settings';
const String spicinessPath = '/spiciness';
const String languagesPath = '/languages';
const String wordDetailPath = '/word-detail';
const String editSuggestionPath = '/edit-suggestion';
const String reportPath = '/report';
const String bookmarksPath = '/bookmarks';
const String wordListPath = '/word-list';
const String searchPath = '/search';

// Route names
const String splashName = 'Splash';
const String signInName = 'Sign In';
const String signUpName = 'Sign Up';
const String forgotPasswordName = 'Forgot Password';
const String resetPasswordNewName = 'Reset Password New';
const String resetPasswordSentName = 'Reset Password Sent';
const String resetPasswordSuccessName = 'Reset Password Success';
const String homeName = 'Home';
const String guestHomeName = 'Guest Home';
const String introName = 'Intro';
const String preferencesName = 'Preferences';
const String accountSettingsName = 'Account Settings';
const String spicinessName = 'Spiciness';
const String languagesName = 'Languages';
const String wordDetailName = 'Word Detail';
const String editSuggestionName = 'Edit Suggestion';
const String reportName = 'Report';
const String bookmarksName = 'Bookmarks';
const String wordListName = 'Word List';
const String searchName = 'Search';

// Query parameters
const String emailParam = 'email';
const String errorDescriptionParam = 'error_description';
const String tokenParam = 'token';
const String wordIdParam = 'word-id';
const String sourceParam = 'source';
const String keyParam = 'key';
