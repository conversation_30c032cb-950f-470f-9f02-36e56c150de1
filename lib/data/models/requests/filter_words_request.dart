import 'package:cussme/domain/domain.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'filter_words_request.freezed.dart';
part 'filter_words_request.g.dart';

@Freezed(toJson: true)
abstract class FilterWordsRequest with _$FilterWordsRequest {
  const factory FilterWordsRequest({
    required String languageId,
    required List<Spiciness> spiciness,
  }) = _FilterWordsRequest;

  factory FilterWordsRequest.fromJson(Map<String, dynamic> json) =>
      _$FilterWordsRequestFromJson(json);

  Map<String, dynamic> toJson() {
    return {
      'languageId': languageId,
      'spiciness': spiciness.map((s) => s.name).toList(),
    };
  }
}
