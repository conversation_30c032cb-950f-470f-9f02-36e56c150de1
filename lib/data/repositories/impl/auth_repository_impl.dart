import 'dart:convert';

import 'package:cussme/data/data.dart';
import 'package:cussme/domain/domain.dart';
import 'package:cussme/localization/generated/l10n.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AuthRepositoryImpl implements AuthRepository {
  final SupabaseClient _supabaseClient;
  final GoogleSignIn _googleSignIn;
  final FacebookAuth _facebookAuth;
  final SharedPreferences _sharedPreferences;

  AuthRepositoryImpl({
    required SupabaseClient supabaseClient,
    required GoogleSignIn googleSignIn,
    required FacebookAuth facebookAuth,
    required SharedPreferences sharedPreferences,
  })  : _supabaseClient = supabaseClient,
        _googleSignIn = googleSignIn,
        _facebookAuth = facebookAuth,
        _sharedPreferences = sharedPreferences;

  @override
  Future<User> signUpWithEmailPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabaseClient.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
      }

      return response.user!;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<User> signInWithEmailPassword({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _supabaseClient.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user == null) {
        throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
      }

      return response.user!;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> upsertProfile({
    required User user,
    required String firstName,
    String? lastName,
  }) async {
    try {
      const profilesTable = SupabaseTable.profiles;
      final profileColumns = profilesTable.columns as ProfileColumns;

      await _supabaseClient.from(profilesTable.name).upsert({
        profileColumns.id: user.id,
        profileColumns.firstName: firstName,
        if (lastName != null) profileColumns.lastName: lastName,
      });
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> deleteCurrentUser() async {
    try {
      await _supabaseClient.deleteUser();
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> signOutSupabase() async {
    try {
      await _supabaseClient.auth.signOut();
    } catch (_) {}
  }

  @override
  Future<void> signOutGoogle() async {
    try {
      final isSignedIn = await _googleSignIn.isSignedIn();
      if (isSignedIn) {
        await _googleSignIn.signOut();
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> clearSharedPreferences() async {
    try {
      await _sharedPreferences.clear();
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> saveUserToSharedPreferences(UserEntity user) async {
    try {
      final userJson = jsonEncode(user.toJson());
      await _sharedPreferences.setString(PrefConstants.user, userJson);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<UserEntity?> getUserFromSharedPreferences() async {
    try {
      final userJson = _sharedPreferences.getString(PrefConstants.user);

      if (userJson == null) {
        return null;
      }

      final userMap = jsonDecode(userJson) as Map<String, dynamic>;
      return UserEntity.fromJson(userMap);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<GoogleSignInAccount> getGoogleSignInAccount() async {
    try {
      // Disconnect from previous Google account to ensure Google Sign-In dialog is shown
      final isSignedIn = await _googleSignIn.isSignedIn();
      if (isSignedIn) {
        await _googleSignIn.disconnect();
      }

      final googleSignInAccount = await _googleSignIn.signIn();

      if (googleSignInAccount == null) {
        throw ExceptionHandler.handleError(
            Str.current.googleSignInNotCompleted);
      }

      return googleSignInAccount;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> signUpWithGoogle(GoogleSignInAccount googleSignInAccount) async {
    try {
      final googleAuth = await googleSignInAccount.authentication;
      final idToken = googleAuth.idToken;
      final accessToken = googleAuth.accessToken;

      if (idToken == null) {
        throw ExceptionHandler.handleError(
            Str.current.failedToInitiateGoogleSignIn);
      }

      final response = await _supabaseClient.auth.signInWithIdToken(
        provider: OAuthProvider.google,
        idToken: idToken,
        accessToken: accessToken,
      );

      if (response.user == null) {
        throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> insertCurrentUserProfile() async {
    try {
      final currentUser = _supabaseClient.auth.currentUser;

      if (currentUser == null) {
        throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
      }

      const profilesTable = SupabaseTable.profiles;
      final profileColumns = profilesTable.columns as ProfileColumns;

      final firstName = currentUser.fullName.extractFirstName();
      final lastName = currentUser.fullName.extractLastName();

      await _supabaseClient.from(profilesTable.name).insert({
        profileColumns.id: currentUser.id,
        profileColumns.firstName: firstName,
        if (lastName != null) profileColumns.lastName: lastName,
      });
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<UserEntity> getProfile() async {
    try {
      final profile = await _supabaseClient.getAggregatedProfile();
      return UserEntity.fromSupabase(profile);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _supabaseClient.auth.resetPasswordForEmail(
        email,
        redirectTo: resetPasswordRedirectUrl,
      );
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> checkIfEmailExists(String email) async {
    try {
      return await _supabaseClient.checkEmailExists(email);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> verifyRecoveryOTP(String token) async {
    try {
      await _supabaseClient.auth.verifyOTP(
        type: OtpType.recovery,
        tokenHash: token,
      );
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> updatePassword(String newPassword) async {
    try {
      await _supabaseClient.auth.updateUser(
        UserAttributes(
          password: newPassword,
        ),
      );
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> updateProfile(String firstName, String? lastName) async {
    try {
      final user = _supabaseClient.auth.currentUser;

      const profilesTable = SupabaseTable.profiles;
      final profileColumns = profilesTable.columns as ProfileColumns;

      await _supabaseClient.from(profilesTable.name).update({
        profileColumns.firstName: firstName,
        profileColumns.lastName: lastName,
      }).eq(profileColumns.id, user?.id ?? defaultUid);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> updateSpiciness(List<Spiciness> spiciness) async {
    try {
      final user = _supabaseClient.auth.currentUser;

      const profilesTable = SupabaseTable.profiles;
      final profileColumns = profilesTable.columns as ProfileColumns;

      await _supabaseClient.from(profilesTable.name).update({
        profileColumns.spiciness: spiciness.map((s) => s.name).toList(),
      }).eq(profileColumns.id, user?.id ?? defaultUid);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> updateLanguages(List<LanguageEntity> languages) async {
    try {
      final currentUserId = _supabaseClient.auth.currentUser!.id;
      final languageIds = languages.map((language) => language.id).toList();

      await _supabaseClient.updateUserLanguages(currentUserId, languageIds);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<List<LanguageEntity>> getAllLanguages() async {
    try {
      const table = SupabaseTable.languages;
      final columns = table.columns as LanguageColumns;

      final response =
          await _supabaseClient.from(table.name).select().order(columns.name);

      return LanguageEntity.fromJsonList(response);
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> isGuest() async {
    return _sharedPreferences.getBool(PrefConstants.isGuest) ?? false;
  }

  @override
  Future<void> setGuest() async {
    await _sharedPreferences.setBool(PrefConstants.isGuest, true);
  }

  @override
  Future<void> removeGuest() async {
    await _sharedPreferences.remove(PrefConstants.isGuest);
  }

  @override
  Future<void> syncCurrentUserToSupabase() async {
    await _supabaseClient.auth.getUser();
  }

  @override
  User? getCurrentSupabaseUser() {
    try {
      return _supabaseClient.auth.currentUser;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<bool> checkIfProfileExists() async {
    try {
      final currentUser = _supabaseClient.auth.currentUser;
      if (currentUser == null) {
        return false;
      }

      const profilesTable = SupabaseTable.profiles;
      final profileColumns = profilesTable.columns as ProfileColumns;

      final existingProfile = await _supabaseClient
          .from(profilesTable.name)
          .select()
          .eq(profileColumns.id, currentUser.id)
          .maybeSingle();

      return existingProfile != null;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> signOutFacebook() async {
    try {
      final isSignedIn = await _facebookAuth.accessToken != null;
      if (isSignedIn) {
        await _facebookAuth.logOut();
      }
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<String> getFacebookAccessToken() async {
    try {
      await _facebookAuth.logOut();

      final loginResult = await _facebookAuth.login();
      if (loginResult.status != LoginStatus.success) {
        throw ExceptionHandler.handleError(Str.current.somethingWentWrong);
      }

      return loginResult.accessToken!.tokenString;
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }

  @override
  Future<void> signUpWithFacebook(String fbAccessToken) async {
    try {
      final response = await http.post(
        Uri.parse(facebookAuthFunctionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': supabaseAnonKey.bearer,
        },
        body: jsonEncode({'fbAccessToken': fbAccessToken}),
      );

      final data = json.decode(response.body);
      final session = data['session'];

      await _supabaseClient.auth.setInitialSession(jsonEncode(session));
      await _supabaseClient.auth.refreshSession();
    } catch (e) {
      throw ExceptionHandler.handleError(e);
    }
  }
}
