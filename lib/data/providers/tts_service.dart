import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_tts/flutter_tts.dart';

class TTSService {
  final Completer<void> _initCompleter = Completer<void>();
  final FlutterTts _flutterTts = FlutterTts();

  TTSService() {
    _init();
  }

  Future<void> _init() async {
    await _flutterTts.setSpeechRate(0.5);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);

    await _flutterTts.awaitSpeakCompletion(true);
    await Future.delayed(const Duration(milliseconds: 200));

    _initCompleter.complete();
  }

  Future<void> speak(String text, String locale) async {
    await _initCompleter.future;
    if (Platform.isIOS) {
      await _flutterTts.setSharedInstance(true);
      await _flutterTts.setIosAudioCategory(
          IosTextToSpeechAudioCategory.playback,
          [
            IosTextToSpeechAudioCategoryOptions.allowBluetooth,
            IosTextToSpeechAudioCategoryOptions.allowBluetoothA2DP,
            IosTextToSpeechAudioCategoryOptions.mixWithOthers,
            IosTextToSpeechAudioCategoryOptions.defaultToSpeaker
          ],
          IosTextToSpeechAudioMode.defaultMode);
    }
    await _flutterTts.stop();
    await _flutterTts.setLanguage(locale);
    await _flutterTts.speak(text);
  }

  Future<void> dispose() async {
    await _flutterTts.stop();
  }
}

final ttsServiceProvider = Provider<TTSService>((ref) {
  final tts = TTSService();
  ref.onDispose(() => tts.dispose());
  return tts;
});
