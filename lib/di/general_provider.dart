import 'dart:io' show Platform;

import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

part 'general_provider.g.dart';

@riverpod
SharedPreferences sharedPreferences(Ref ref) {
  //implemented in main method
  throw UnimplementedError();
}

@riverpod
GoogleSignIn googleSignIn(Ref ref) {
  final String clientId = Platform.isIOS
      ? '43777014067-9vqai89ogjljh5fqckvlv2hve8r7bh3d.apps.googleusercontent.com'
      : '43777014067-med5k3h72uhe2tefc8gu4mne4ck9si0j.apps.googleusercontent.com';

  return GoogleSignIn(
    clientId: clientId,
    scopes: ['email', 'https://www.googleapis.com/auth/userinfo.profile'],
  );
}

@riverpod
FacebookAuth facebookAuth(Ref ref) {
  return FacebookAuth.instance;
}

@riverpod
SupabaseClient supabaseClient(Ref ref) {
  return Supabase.instance.client;
}
